import React, {useEffect, useState} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogFooter} from "@/components/ui/dialog";
import {But<PERSON>} from "@/components/ui/button";
import {AlertCircle, ArrowRight, Check, FileAudio, FileText, Image, Settings, Wallet} from "lucide-react";
import {Progress} from "@/components/ui/progress";
import {toast} from "sonner";
import {MAX_FILE_SIZE, uploadFileToS3, validateFileSize} from "@/services/uploadService";

import {v4 as uuidv4} from 'uuid';
import {VideoGenerationMode} from "@/utils/videoGenerationUtils";
import {Input} from "@/components/ui/input";
import {getResourceFromDB, storeResourceInDB} from "@/utils/indexedDBUtils";
import {useNavigate} from "react-router-dom";
import {API_BASE_URL, authenticatedFetch} from "@/utils/authUtils";

type VideoGenerationStep = "configure" | "review" | "generate";
type VideoModel = "LCF-base" | "LCF-pro" | "LCF-ultra";
type VideoResolution = "FullHD" | "2K" | "4K";

interface VideoConfiguration {
  model: VideoModel;
  resolution: VideoResolution;
  cropImage: boolean;
}

interface VideoGenerationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pendingImage: File | null;
  pendingAudio: File | null;
  pendingPptx: File | null;
  selectedMode: VideoGenerationMode;
  pageNotes: string[];
  onUploadStatusChange: (type: 'image' | 'audio' | 'pptx', isUploading: boolean) => void;
  onFileUploadComplete: (type: 'image' | 'audio' | 'pptx', url: string) => void;
}

export const VideoGenerationDialog = ({
  open,
  onOpenChange,
  pendingImage,
  pendingAudio,
  pendingPptx,
  selectedMode,
  pageNotes,
  onUploadStatusChange,
  onFileUploadComplete
}: VideoGenerationDialogProps) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<VideoGenerationStep>("configure");
  const [configuration, setConfiguration] = useState<VideoConfiguration>({
    model: "LCF-base",
    resolution: "2K",
    cropImage: false,
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [generationId, setGenerationId] = useState<string>("");
  const [fileSizeErrors, setFileSizeErrors] = useState<{[key: string]: boolean}>({
    image: false,
    audio: false,
    pptx: false
  });
  const [validatedNotes, setValidatedNotes] = useState<string[]>([]);
  const [noteErrors, setNoteErrors] = useState<{[index: number]: boolean}>({});
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [userTokenBalance, setUserTokenBalance] = useState<number | null>(null);
  const [isTokenValidating, setIsTokenValidating] = useState(false);
  const [hasEnoughTokens, setHasEnoughTokens] = useState<boolean | null>(null);

  // Reset dialog state when opening
  React.useEffect(() => {
    if (open) {
      setCurrentStep("configure");
      setIsProcessing(false);
      setUploadProgress(0);
      setGenerationId("");
      setFileSizeErrors({
        image: false,
        audio: false,
        pptx: false
      });
      validateNotes();
      setPreviewImageUrl(null);

      // Validate file sizes when the dialog opens
      checkFileSizes();
    }
  }, [open, pendingImage, pendingAudio, pendingPptx]);



  // Validate notes when pageNotes change
  useEffect(() => {
    validateNotes();
  }, [pageNotes]);

  // Fetch preview image when configuration changes or when entering review step
  useEffect(() => {
    if (currentStep === "review") {
      fetchPreviewImage();
      validateUserTokens();
    }
  }, [configuration.model, configuration.cropImage, currentStep]);

  // Generate a cache key based on configuration settings
  const getPreviewCacheKey = (model: string, cropImage: boolean): string => {
    return `preview_${model}_crop_${cropImage}`;
  };

  // Fetch preview image based on model and crop settings with caching
  const fetchPreviewImage = async () => {
    setIsPreviewLoading(true);
    try {
      const cacheKey = getPreviewCacheKey(configuration.model, configuration.cropImage);

      // Try to get image from IndexedDB cache first
      const cachedImage = await getResourceFromDB(cacheKey);

      if (cachedImage) {
        console.log('Loading preview image from cache');
        const imageUrl = URL.createObjectURL(cachedImage);
        setPreviewImageUrl(imageUrl);
        setIsPreviewLoading(false);
        return;
      }

      // If not in cache, fetch from API
      console.log('Fetching preview image from API');
      const apiUrl = new URL(`${API_BASE_URL}/media/download`);
      apiUrl.searchParams.append('model', configuration.model);
      apiUrl.searchParams.append('crop', configuration.cropImage.toString());

      const response = await authenticatedFetch(apiUrl.toString());

      if (!response.ok) {
        throw new Error('Failed to fetch preview image');
      }

      const data = await response.json();

      if (data && data.body) {
        // The presigned URL is the body itself
        const presignedUrl = data.body;

        // Fetch the actual image using the presigned URL
        const imageResponse = await fetch(presignedUrl);
        if (!imageResponse.ok) {
          throw new Error('Failed to fetch image from presigned URL');
        }

        // Get the image blob and store in cache
        const imageBlob = await imageResponse.blob();
        await storeResourceInDB(cacheKey, imageBlob, 'image');

        // Create object URL for display
        const imageUrl = URL.createObjectURL(imageBlob);
        setPreviewImageUrl(imageUrl);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching preview image:', error);
      toast.error('Failed to load preview image');
      setPreviewImageUrl(null);
    } finally {
      setIsPreviewLoading(false);
    }
  };

  // Validate the notes
  const validateNotes = () => {
    const alphanumericRegex = /^[a-zA-Z0-9.,\s]*[a-zA-Z]+[a-zA-Z0-9.,\s]*$/;
    const errors: {[index: number]: boolean} = {};
    let hasErrors = false;

    const validated = pageNotes.map((note, index) => {
      // Check if the note contains at least one alphabetical character and only allowed characters
      if (!alphanumericRegex.test(note)) {
        errors[index] = true;
        hasErrors = true;
        return note; // Keep the original note for user correction
      }
      return note;
    });

    setNoteErrors(errors);
    setValidatedNotes(validated);

    return !hasErrors;
  };

  // Check file sizes for all pending files
  const checkFileSizes = () => {
    const errors = {
      image: false,
      audio: false,
      pptx: false
    };

    if (pendingImage && !validateFileSize(pendingImage)) {
      errors.image = true;
    }

    if (pendingAudio && !validateFileSize(pendingAudio)) {
      errors.audio = true;
    }

    if (pendingPptx && !validateFileSize(pendingPptx)) {
      errors.pptx = true;
    }

    setFileSizeErrors(errors);

    return !(errors.image || errors.audio || errors.pptx);
  };

  // Check if we can proceed to generation
  const canGenerate = Boolean(
    (pendingImage && !fileSizeErrors.image) &&
    (pendingAudio && !fileSizeErrors.audio) &&
    (pendingPptx && !fileSizeErrors.pptx) &&
    Object.keys(noteErrors).length === 0 &&
    validatedNotes.length === pageNotes.length &&
    pageNotes.every(note => note.trim() !== "") &&
    (currentStep !== "review" || hasEnoughTokens === true)
  );

  // Calculate estimated token cost (simplified example)
  const calculateTokenCost = (): number => {
    const baseTokens = {
      "LCF-base": 100,
      "LCF-pro": 200,
      "LCF-ultra": 300
    }[configuration.model];

    const resolutionMultiplier = {
      "FullHD": 1,
      "2K": 1.05,
      "4K": 1.1
    }[configuration.resolution];

    return Math.round(baseTokens * resolutionMultiplier * pageNotes.length);
  };

  const handleConfigChange = (key: keyof VideoConfiguration, value: any) => {
    setConfiguration(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Validate user tokens against the estimated cost
  const validateUserTokens = async () => {
    setIsTokenValidating(true);
    setHasEnoughTokens(null);

    try {
      // Make a GET request to the token API
      const apiUrl = new URL(`${API_BASE_URL}/users/information`);
      const response = await authenticatedFetch(apiUrl.toString());

      if (!response.ok) {
        throw new Error('Failed to fetch user token balance');
      }

      const data = await response.json();

      if (data && data.body !== undefined) {
        const tokenBalance = parseInt(data.body.token, 10);
        setUserTokenBalance(tokenBalance);

        // Compare with estimated token cost
        const estimatedCost = calculateTokenCost();
        const sufficient = tokenBalance >= estimatedCost;
        setHasEnoughTokens(sufficient);

        if (!sufficient) {
          toast.warning('You don\'t have enough tokens for this generation');
        }
      } else {
        throw new Error('Invalid token data format');
      }
    } catch (error) {
      console.error('Error validating user tokens:', error);
      toast.error('Failed to validate your token balance');
      setHasEnoughTokens(null);
    } finally {
      setIsTokenValidating(false);
    }
  };

  const handleNext = () => {
    if (currentStep === "configure") {
      if (!validateNotes()) {
        toast.error("Please fix the notes for all slides");
        return;
      }
      setCurrentStep("review");
    } else if (currentStep === "review") {
      // Validate file sizes, notes, and token balance before proceeding to generation
      if (!hasEnoughTokens) {
        toast.error("You don't have enough tokens for this generation");
        return;
      }

      if (checkFileSizes() && validateNotes()) {
        setCurrentStep("generate");
        startVideoGeneration();
      } else if (!validateNotes()) {
        toast.error("Please fix the notes for all slides");
      } else {
        toast.error("Some files exceed the 10MB size limit");
      }
    }
  };

  const handleBack = () => {
    if (currentStep === "review") {
      setCurrentStep("configure");
    } else if (currentStep === "generate") {
      setCurrentStep("review");
      setIsProcessing(false);
    }
  };

  // Upload a single file and return its URL
  const handleUploadFile = async (file: File, type: 'image' | 'audio' | 'pptx', uuid: string) => {
    if (!file) return null;

    try {
      // Validate file size again before upload
      if (!validateFileSize(file)) {
        throw new Error(`File size exceeds the limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
      }

      onUploadStatusChange(type, true);

      const prefix = type === 'image' ? 'images' :
                    type === 'audio' ? 'audios' : 'pdfs';

      const fileExtension = file.name.split('.').pop();
      const newFileName = `${type}_${uuid}.${fileExtension}`;

      const url = await uploadFileToS3({ prefix: prefix, name: newFileName, file: file });

      console.log(`File ${newFileName} uploaded successfully to ${url}`);
      onFileUploadComplete(type, url);

      return url;
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      toast.error(`Failed to upload ${type}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      onUploadStatusChange(type, false);
      return null;
    }
  };

  const startVideoGeneration = async () => {
    setIsProcessing(true);
    setUploadProgress(0);

    try {
      // Generate unique identifier for this video generation
      const uuid = uuidv4();
      setGenerationId(uuid);
      console.log(`Starting video generation with ID: ${uuid}`);
      console.log(`Using pageNotes.length: ${pageNotes.length}`);

      // Prepare file uploads
      const filesToUpload = [];
      let uploadCount = 0;
      let totalUploads = 0;

      // Image upload
      if (pendingImage) {
        if (!validateFileSize(pendingImage)) {
          toast.error(`Image file exceeds the 10MB size limit`);
          setIsProcessing(false);
          return;
        }

        filesToUpload.push({
          file: pendingImage,
          type: "image"
        });
        totalUploads++;
      }

      // Audio upload
      if (pendingAudio) {
        if (!validateFileSize(pendingAudio)) {
          toast.error(`Audio file exceeds the 10MB size limit`);
          setIsProcessing(false);
          return;
        }

        filesToUpload.push({
          file: pendingAudio,
          type: "audio"
        });
        totalUploads++;
      }

      // Presentation upload
      if (pendingPptx) {
        if (!validateFileSize(pendingPptx)) {
          toast.error(`Presentation file exceeds the 10MB size limit`);
          setIsProcessing(false);
          return;
        }

        filesToUpload.push({
          file: pendingPptx,
          type: "pptx"
        });
        totalUploads++;
      }

      // Create configs JSON file
      const configData = new Blob([JSON.stringify({
        model: configuration.model,
        resolution: configuration.resolution,
        cropImage: configuration.cropImage,
        mode: selectedMode.id,
        slideCount: pageNotes.length // Use pageNotes.length directly
      })], { type: 'application/json' });

      const configFile = new File([configData], `configs_${uuid}.json`, { type: 'application/json' });
      filesToUpload.push({
        file: configFile,
        prefix: "configs",
        name: `configs_${uuid}.json`,
        type: "config"
      });
      totalUploads++;

      // Create text JSON file with actual text data from page notes
      const processedNotes = [...pageNotes];

      console.log(processedNotes);

      const slidesWithText = Array(processedNotes.length).fill(null).map((_, i) => ({
        slideNumber: i + 1,
        text: processedNotes[i] || ""
      }));

      console.log(`Using slide text data for ${processedNotes.length} slides:`, slidesWithText);

      const textData = new Blob([JSON.stringify({
        slides: slidesWithText
      })], { type: 'application/json' });

      const textFile = new File([textData], `text_${uuid}.json`, { type: 'application/json' });
      filesToUpload.push({
        file: textFile,
        prefix: "texts",
        name: `text_${uuid}.json`,
        type: "text"
      });
      totalUploads++;

      // Upload all files
      for (const fileInfo of filesToUpload) {
        try {
          console.log(`Uploading ${fileInfo.type}: ${fileInfo.name || fileInfo.file.name}`);

          if (fileInfo.type === 'image' || fileInfo.type === 'audio' || fileInfo.type === 'pptx') {
            await handleUploadFile(fileInfo.file, fileInfo.type, uuid);
          } else {
            // For config and text files
            await uploadFileToS3({
              prefix: fileInfo.prefix,
              name: fileInfo.name,
              file: fileInfo.file
            });
          }

          uploadCount++;
          setUploadProgress(Math.round((uploadCount / totalUploads) * 100));
        } catch (error) {
          console.error(`Error uploading ${fileInfo.type}:`, error);
          toast.error(`Failed to upload ${fileInfo.name || fileInfo.file.name}`);
          setIsProcessing(false);
          return;
        }
      }

      // Notify user that video generation is starting
      toast.info("Video generation started");

      // Wait for 5 seconds before making the API call
      await new Promise(resolve => setTimeout(resolve, 5000));

      try {
        // Prepare the request body
        const requestBody = {
          session: uuid,
          image: `image_${uuid}.${pendingImage ? pendingImage.name.split('.').pop() : 'png'}`,
          audio: `audio_${uuid}.${pendingAudio ? pendingAudio.name.split('.').pop() : 'wav'}`,
          text: `text_${uuid}.json`,
          config: `configs_${uuid}.json`,
          pdf: `pptx_${uuid}.${pendingPptx ? pendingPptx.name.split('.').pop() : 'pdf'}`,
          time: Math.floor(Date.now() / 1000), // Current epoch time
          token: calculateTokenCost()
        };

        console.log('Sending session data to API:', requestBody);

        // Make the POST request to the API
        const response = await authenticatedFetch(`${API_BASE_URL}/sessions/lambda-create/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        const responseData = await response.json();
        console.log('API response:', responseData);

        // Check the response status
        if (responseData.statusCode === 200) {
          toast.success("Your video generation has been queued successfully");

          // Close dialog after successful submission and redirect to Session page
          setTimeout(() => {
            onOpenChange(false);
            navigate("/session");
          }, 2000);
        } else {
          // Handle error response
          const errorMessage = responseData.body || 'Failed to queue video generation';
          toast.error(`Error: ${errorMessage}`);
        }
      } catch (error) {
        console.error('Error sending session data:', error);
        toast.error("Failed to queue video generation. Please try again.");
      }

    } catch (error) {
      console.error("Error during video generation:", error);
      toast.error("Failed to generate video. Please try again.");
      setIsProcessing(false);
    }
  };

  // Format file size to human-readable format
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Clean up object URLs when component unmounts or when URL changes
  useEffect(() => {
    return () => {
      if (previewImageUrl && previewImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewImageUrl);
      }
    };
  }, [previewImageUrl]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {/* Update the DialogContent to have a maximum height with scrolling */}
      <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[85vh] overflow-y-auto">
        {/* Dialog header with steps indicator */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <div className={`flex flex-col items-center ${currentStep === "configure" ? "text-primary" : "text-muted-foreground"}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === "configure" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>1</div>
              <span className="text-xs">Configure</span>
            </div>

            <div className="flex-1 h-px bg-muted mx-2"></div>

            <div className={`flex flex-col items-center ${currentStep === "review" ? "text-primary" : "text-muted-foreground"}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === "review" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>2</div>
              <span className="text-xs">Review</span>
            </div>

            <div className="flex-1 h-px bg-muted mx-2"></div>

            <div className={`flex flex-col items-center ${currentStep === "generate" ? "text-primary" : "text-muted-foreground"}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === "generate" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>3</div>
              <span className="text-xs">Generate</span>
            </div>
          </div>
        </div>

        {/* Configure step */}
        {currentStep === "configure" && (
          <>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Configure Your Video</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Model</label>
                  <div className="grid grid-cols-3 gap-2">
                    {(["LCF-base", "LCF-pro", "LCF-ultra"] as VideoModel[]).map((model) => (
                      <Button
                        key={model}
                        variant={configuration.model === model ? "default" : "outline"}
                        onClick={() => handleConfigChange("model", model)}
                        className="justify-center"
                      >
                        {model}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Resolution</label>
                  <div className="grid grid-cols-3 gap-2">
                    {(["FullHD", "2K", "4K"] as VideoResolution[]).map((resolution) => (
                      <Button
                        key={resolution}
                        variant={configuration.resolution === resolution ? "default" : "outline"}
                        onClick={() => handleConfigChange("resolution", resolution)}
                        className="justify-center"
                      >
                        {resolution}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="crop-image"
                    checked={configuration.cropImage}
                    onChange={(e) => handleConfigChange("cropImage", e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="crop-image" className="text-sm">Crop Image</label>
                </div>

                {/* Slide Notes Validation Section */}
                {pageNotes.length > 0 && (
                  <div className="space-y-3 mt-4">
                    <h4 className="text-sm font-medium">Slide Notes</h4>
                    <p className="text-xs text-muted-foreground">
                      Each slide must have notes containing at least one alphabetical character.
                      Only letters, numbers, periods, and commas are allowed.
                    </p>

                    <div className="space-y-3 max-h-60 overflow-y-auto p-2 border rounded-md">
                      {pageNotes.map((note, index) => (
                        <div key={`note-${index}`} className="space-y-1">
                          <label className="text-xs font-medium flex justify-between">
                            <span>Slide {index + 1}</span>
                            {noteErrors[index] && (
                              <span className="text-red-500 flex items-center">
                                <AlertCircle className="h-3 w-3 mr-1" /> Invalid format
                              </span>
                            )}
                          </label>
                          <Input
                            value={note}
                            onChange={(e) => {
                              const newNotes = [...pageNotes];
                              const validInput = e.target.value.replace(/[^a-zA-Z0-9.,\s]/g, '');
                              newNotes[index] = validInput;

                              // Update the pageNotes in the parent component
                              // This would normally be done through a callback prop,
                              // but for simplicity we're just validating on our end
                              validateNotes();
                            }}
                            className={noteErrors[index] ? "border-red-500" : ""}
                            placeholder="Enter notes for this slide"
                          />
                          {noteErrors[index] && (
                            <p className="text-xs text-red-500">
                              Must contain at least one letter and only use allowed characters
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className="mt-4">
              <Button
                onClick={handleNext}
                className="ml-auto"
                disabled={Object.keys(noteErrors).length > 0 || pageNotes.some(note => note.trim() === "")}
              >
                Next <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </DialogFooter>
          </>
        )}

        {/* Review step */}
        {currentStep === "review" && (
          <>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Review & Confirm</h3>

              <div className="space-y-4">
                <div className="bg-muted/50 p-4 rounded-md space-y-3">
                  <h4 className="font-medium text-sm">Configuration</h4>

                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-muted-foreground">Model:</div>
                    <div>{configuration.model}</div>

                    <div className="text-muted-foreground">Resolution:</div>
                    <div>{configuration.resolution}</div>

                    <div className="text-muted-foreground">Crop Image:</div>
                    <div>{configuration.cropImage ? "Yes" : "No"}</div>

                    <div className="text-muted-foreground">Generation Mode:</div>
                    <div>{selectedMode.name}</div>

                    <div className="text-muted-foreground">Slides:</div>
                    <div>{pageNotes.length}</div>
                  </div>
                </div>

                {/* Preview Image Section */}
                <div className="bg-muted/50 p-4 rounded-md space-y-3">
                  <h4 className="font-medium text-sm">Preview</h4>
                  <p className="text-xs text-muted-foreground">
                    This is a preview of how your video might look with the selected model and crop settings.
                  </p>

                  <div className="aspect-video bg-black/5 rounded-md overflow-hidden flex items-center justify-center">
                    {isPreviewLoading ? (
                      <div className="flex flex-col items-center justify-center p-4">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
                        <p className="text-sm mt-2">Loading preview...</p>
                      </div>
                    ) : previewImageUrl ? (
                      <img
                        src={previewImageUrl}
                        alt="Model preview"
                        className="w-full h-auto object-contain"
                        onError={() => {
                          toast.error('Failed to load preview image');
                          setPreviewImageUrl(null);
                        }}
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center p-4">
                        <Image className="h-10 w-10 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">Preview not available</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-muted/50 p-4 rounded-md space-y-3">
                  <h4 className="font-medium text-sm">Files</h4>

                  <div className="space-y-2">
                    <div className={`flex items-center ${fileSizeErrors.image ? "text-red-500" : ""}`}>
                      <Image className="h-4 w-4 mr-2" />
                      <span className="text-sm">
                        {pendingImage?.name || "No image selected"}
                        {pendingImage && (
                          <span className="ml-2 text-xs">
                            ({formatFileSize(pendingImage.size)})
                            {fileSizeErrors.image && " - File too large (max 10MB)"}
                          </span>
                        )}
                      </span>
                    </div>

                    <div className={`flex items-center ${fileSizeErrors.audio ? "text-red-500" : ""}`}>
                      <FileAudio className="h-4 w-4 mr-2" />
                      <span className="text-sm">
                        {pendingAudio?.name || "No audio selected"}
                        {pendingAudio && (
                          <span className="ml-2 text-xs">
                            ({formatFileSize(pendingAudio.size)})
                            {fileSizeErrors.audio && " - File too large (max 10MB)"}
                          </span>
                        )}
                      </span>
                    </div>

                    <div className={`flex items-center ${fileSizeErrors.pptx ? "text-red-500" : ""}`}>
                      <FileText className="h-4 w-4 mr-2" />
                      <span className="text-sm">
                        {pendingPptx?.name || "No presentation selected"}
                        {pendingPptx && (
                          <span className="ml-2 text-xs">
                            ({formatFileSize(pendingPptx.size)})
                            {fileSizeErrors.pptx && " - File too large (max 10MB)"}
                          </span>
                        )}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between bg-muted/50 p-4 rounded-md">
                  <div className="flex items-center">
                    <Settings className="h-4 w-4 mr-2" />
                    <span className="text-sm font-medium">Estimated token cost:</span>
                  </div>
                  <div className="font-bold">{calculateTokenCost().toLocaleString()} tokens</div>
                </div>

                <div className={`flex items-center justify-between p-4 rounded-md ${hasEnoughTokens === false ? 'bg-red-50' : hasEnoughTokens === true ? 'bg-green-50' : 'bg-muted/50'}`}>
                  <div className="flex items-center">
                    <Wallet className="h-4 w-4 mr-2" />
                    <span className="text-sm font-medium">Your token balance:</span>
                  </div>
                  <div className="flex items-center">
                    {isTokenValidating ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-2"></div>
                        <span className="text-sm">Validating...</span>
                      </div>
                    ) : userTokenBalance !== null ? (
                      <div className={`font-bold ${hasEnoughTokens === false ? 'text-red-600' : hasEnoughTokens === true ? 'text-green-600' : ''}`}>
                        {userTokenBalance.toLocaleString()} tokens
                        {hasEnoughTokens === false && (
                          <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">
                            Insufficient
                          </span>
                        )}
                        {hasEnoughTokens === true && (
                          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                            Sufficient
                          </span>
                        )}
                      </div>
                    ) : (
                      <span className="text-sm text-muted-foreground">Unable to validate</span>
                    )}
                  </div>
                </div>

                {!canGenerate && (
                  <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm">
                    <p className="font-semibold">Missing or invalid files required for video generation:</p>
                    <ul className="list-disc list-inside mt-1">
                      {!pendingImage && <li>Image file</li>}
                      {fileSizeErrors.image && <li>Image file exceeds 10MB size limit</li>}
                      {!pendingAudio && <li>Audio file</li>}
                      {fileSizeErrors.audio && <li>Audio file exceeds 10MB size limit</li>}
                      {!pendingPptx && <li>Presentation file (PPTX/PDF)</li>}
                      {fileSizeErrors.pptx && <li>Presentation file exceeds 10MB size limit</li>}
                      {Object.keys(noteErrors).length > 0 && <li>Invalid slide notes format</li>}
                      {pageNotes.some(note => note.trim() === "") && <li>Some slides have empty notes</li>}
                      {hasEnoughTokens === false && <li>Insufficient token balance for this generation</li>}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className="mt-4 sticky bottom-0 pt-2 bg-background">
              <Button
                variant="outline"
                onClick={handleBack}
              >
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!canGenerate}
              >
                Confirm
              </Button>
            </DialogFooter>
          </>
        )}

        {/* Generate step */}
        {currentStep === "generate" && (
          <>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Generate Video</h3>

              <div className="space-y-6">
                {isProcessing ? (
                  <div className="space-y-4">
                    <div className="flex flex-col items-center justify-center py-6">
                      <div className="w-full mb-4">
                        <Progress value={uploadProgress} className="h-2" />
                      </div>
                      <p className="text-sm text-center">
                        {uploadProgress < 100
                          ? `Uploading files (${uploadProgress}%)...`
                          : "Processing video generation..."}
                      </p>

                      {uploadProgress === 100 && (
                        <div className="mt-4 flex flex-col items-center">
                          <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
                            <Check className="h-6 w-6 text-green-600" />
                          </div>
                          <p className="text-sm font-medium text-center">Video generation started!</p>
                          <p className="text-xs text-muted-foreground text-center mt-1">
                            Your video is being processed and will be available soon.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-yellow-50 text-yellow-800 p-4 rounded-md text-sm">
                      <p className="font-semibold">Before proceeding, please ensure:</p>
                      <ul className="list-disc list-inside mt-2 space-y-1">
                        <li>All necessary files (image, audio, presentation) are attached</li>
                        <li>Each slide in the Presentation Viewer has corresponding text content</li>
                        <li>Each file size is under 10MB</li>
                      </ul>
                    </div>

                    <div className="bg-muted/50 p-4 rounded-md">
                      <h4 className="font-medium text-sm mb-2">Files to be uploaded:</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center">
                          <Image className="h-4 w-4 mr-2" />
                          <span>image_{"{uuid}"}.png</span>
                        </div>
                        <div className="flex items-center">
                          <FileAudio className="h-4 w-4 mr-2" />
                          <span>audio_{"{uuid}"}.wav</span>
                        </div>
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2" />
                          <span>presentation_{"{uuid}"}.pdf</span>
                        </div>
                        <div className="flex items-center">
                          <Settings className="h-4 w-4 mr-2" />
                          <span>configs_{"{uuid}"}.json</span>
                        </div>
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2" />
                          <span>text_{"{uuid}"}.json</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className="mt-4 sticky bottom-0 pt-2 bg-background">
              {!isProcessing && (
                <>
                  <Button
                    variant="outline"
                    onClick={handleBack}
                  >
                    Back
                  </Button>
                  <Button
                    onClick={startVideoGeneration}
                    disabled={!canGenerate}
                  >
                    Start Generation
                  </Button>
                </>
              )}
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
